// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  username      String   @unique
  password_hash String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  boards Board[]
}

model Board {
  id        String   @id @default(cuid())
  name      String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  columns Column[]
}

model Column {
  id        String   @id @default(cuid())
  title     String
  position  Int
  boardId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  board Board   @relation(fields: [boardId], references: [id], onDelete: Cascade)
  tasks Task[]
}

model Task {
  id          String   @id @default(cuid())
  title       String
  description String?
  position    Int
  columnId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  column Column @relation(fields: [columnId], references: [id], onDelete: Cascade)
}