import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { db } from '@/lib/db';
import { withAuth } from '@/lib/middleware';

const createColumnSchema = z.object({
  title: z.string().min(1, 'Column title is required'),
  boardId: z.string().min(1, 'Board ID is required'),
  position: z.number().int().min(0, 'Position must be a non-negative integer'),
});

const updateColumnSchema = z.object({
  title: z.string().min(1, 'Column title is required').optional(),
  position: z.number().int().min(0, 'Position must be a non-negative integer').optional(),
});

// POST create new column
async function createColumnHandler(request: NextRequest, user: any) {
  try {
    const body = await request.json();
    const { title, boardId, position } = createColumnSchema.parse(body);

    // Check if board exists and belongs to user
    const board = await db.board.findFirst({
      where: {
        id: boardId,
        userId: user.id
      }
    });

    if (!board) {
      return NextResponse.json(
        { error: 'Board not found' },
        { status: 404 }
      );
    }

    const column = await db.column.create({
      data: {
        title,
        boardId,
        position
      },
      include: {
        tasks: {
          orderBy: { position: 'asc' }
        }
      }
    });

    return NextResponse.json({ column }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Create column error:', error);
    return NextResponse.json(
      { error: 'Failed to create column' },
      { status: 500 }
    );
  }
}

export const POST = withAuth(createColumnHandler);