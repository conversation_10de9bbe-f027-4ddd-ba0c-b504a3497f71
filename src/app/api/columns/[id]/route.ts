import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { db } from '@/lib/db';
import { withAuth } from '@/lib/middleware';

const updateColumnSchema = z.object({
  title: z.string().min(1, 'Column title is required').optional(),
  position: z.number().int().min(0, 'Position must be a non-negative integer').optional(),
});

// PUT update column
async function updateColumnHandler(request: NextRequest, user: any, { params }: { params: { id: string } }) {
  try {
    const body = await request.json();
    const { title, position } = updateColumnSchema.parse(body);

    // Check if column exists and belongs to user's board
    const column = await db.column.findFirst({
      where: {
        id: params.id,
        board: {
          userId: user.id
        }
      }
    });

    if (!column) {
      return NextResponse.json(
        { error: 'Column not found' },
        { status: 404 }
      );
    }

    const updatedColumn = await db.column.update({
      where: { id: params.id },
      data: {
        ...(title && { title }),
        ...(position !== undefined && { position })
      },
      include: {
        tasks: {
          orderBy: { position: 'asc' }
        }
      }
    });

    return NextResponse.json({ column: updatedColumn });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Update column error:', error);
    return NextResponse.json(
      { error: 'Failed to update column' },
      { status: 500 }
    );
  }
}

// DELETE column
async function deleteColumnHandler(request: NextRequest, user: any, { params }: { params: { id: string } }) {
  try {
    // Check if column exists and belongs to user's board
    const column = await db.column.findFirst({
      where: {
        id: params.id,
        board: {
          userId: user.id
        }
      }
    });

    if (!column) {
      return NextResponse.json(
        { error: 'Column not found' },
        { status: 404 }
      );
    }

    await db.column.delete({
      where: { id: params.id }
    });

    return NextResponse.json({ message: 'Column deleted successfully' });
  } catch (error) {
    console.error('Delete column error:', error);
    return NextResponse.json(
      { error: 'Failed to delete column' },
      { status: 500 }
    );
  }
}

export const PUT = (request: NextRequest, context: any) => 
  withAuth((req: NextRequest, user: any) => updateColumnHandler(req, user, context))(request);

export const DELETE = (request: NextRequest, context: any) => 
  withAuth((req: NextRequest, user: any) => deleteColumnHandler(req, user, context))(request);