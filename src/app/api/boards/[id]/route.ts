import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { db } from '@/lib/db';
import { withAuth } from '@/lib/middleware';

const updateBoardSchema = z.object({
  name: z.string().min(1, 'Board name is required').optional(),
});

// GET single board
async function getBoardHandler(request: NextRequest, user: any, { params }: { params: { id: string } }) {
  try {
    const board = await db.board.findFirst({
      where: {
        id: params.id,
        userId: user.id
      },
      include: {
        columns: {
          include: {
            tasks: {
              orderBy: { position: 'asc' }
            }
          },
          orderBy: { position: 'asc' }
        }
      }
    });

    if (!board) {
      return NextResponse.json(
        { error: 'Board not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ board });
  } catch (error) {
    console.error('Get board error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch board' },
      { status: 500 }
    );
  }
}

// PUT update board
async function update<PERSON>oard<PERSON><PERSON><PERSON>(request: NextRequest, user: any, { params }: { params: { id: string } }) {
  try {
    const body = await request.json();
    const { name } = updateBoardSchema.parse(body);

    // Check if board exists and belongs to user
    const existingBoard = await db.board.findFirst({
      where: {
        id: params.id,
        userId: user.id
      }
    });

    if (!existingBoard) {
      return NextResponse.json(
        { error: 'Board not found' },
        { status: 404 }
      );
    }

    const board = await db.board.update({
      where: { id: params.id },
      data: { name },
      include: {
        columns: {
          include: {
            tasks: {
              orderBy: { position: 'asc' }
            }
          },
          orderBy: { position: 'asc' }
        }
      }
    });

    return NextResponse.json({ board });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Update board error:', error);
    return NextResponse.json(
      { error: 'Failed to update board' },
      { status: 500 }
    );
  }
}

// DELETE board
async function deleteBoardHandler(request: NextRequest, user: any, { params }: { params: { id: string } }) {
  try {
    // Check if board exists and belongs to user
    const existingBoard = await db.board.findFirst({
      where: {
        id: params.id,
        userId: user.id
      }
    });

    if (!existingBoard) {
      return NextResponse.json(
        { error: 'Board not found' },
        { status: 404 }
      );
    }

    await db.board.delete({
      where: { id: params.id }
    });

    return NextResponse.json({ message: 'Board deleted successfully' });
  } catch (error) {
    console.error('Delete board error:', error);
    return NextResponse.json(
      { error: 'Failed to delete board' },
      { status: 500 }
    );
  }
}

export const GET = (request: NextRequest, context: any) => 
  withAuth((req: NextRequest, user: any) => getBoardHandler(req, user, context))(request);

export const PUT = (request: NextRequest, context: any) => 
  withAuth((req: NextRequest, user: any) => updateBoardHandler(req, user, context))(request);

export const DELETE = (request: NextRequest, context: any) => 
  withAuth((req: NextRequest, user: any) => deleteBoardHandler(req, user, context))(request);