import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { db } from '@/lib/db';
import { withAuth } from '@/lib/middleware';

const createBoardSchema = z.object({
  name: z.string().min(1, 'Board name is required'),
});

// GET all boards for authenticated user
async function getBoardsHandler(request: NextRequest, user: any) {
  try {
    const boards = await db.board.findMany({
      where: { userId: user.id },
      include: {
        columns: {
          include: {
            tasks: {
              orderBy: { position: 'asc' }
            }
          },
          orderBy: { position: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({ boards });
  } catch (error) {
    console.error('Get boards error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch boards' },
      { status: 500 }
    );
  }
}

// POST create new board
async function createBoard<PERSON><PERSON><PERSON>(request: NextRequest, user: any) {
  try {
    const body = await request.json();
    const { name } = createBoardSchema.parse(body);

    // Create board with default columns
    const board = await db.board.create({
      data: {
        name,
        userId: user.id,
        columns: {
          create: [
            { title: 'To Do', position: 0 },
            { title: 'In Progress', position: 1 },
            { title: 'Done', position: 2 }
          ]
        }
      },
      include: {
        columns: {
          include: {
            tasks: {
              orderBy: { position: 'asc' }
            }
          },
          orderBy: { position: 'asc' }
        }
      }
    });

    return NextResponse.json({ board }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Create board error:', error);
    return NextResponse.json(
      { error: 'Failed to create board' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(getBoardsHandler);
export const POST = withAuth(createBoardHandler);