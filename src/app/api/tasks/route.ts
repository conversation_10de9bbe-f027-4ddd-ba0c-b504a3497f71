import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { db } from '@/lib/db';
import { withAuth } from '@/lib/middleware';

const createTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required'),
  description: z.string().optional(),
  columnId: z.string().min(1, 'Column ID is required'),
  position: z.number().int().min(0, 'Position must be a non-negative integer'),
});

// POST create new task
async function createTaskHandler(request: NextRequest, user: any) {
  try {
    const body = await request.json();
    const { title, description, columnId, position } = createTaskSchema.parse(body);

    // Check if column exists and belongs to user's board
    const column = await db.column.findFirst({
      where: {
        id: columnId,
        board: {
          userId: user.id
        }
      }
    });

    if (!column) {
      return NextResponse.json(
        { error: 'Column not found' },
        { status: 404 }
      );
    }

    const task = await db.task.create({
      data: {
        title,
        description,
        columnId,
        position
      }
    });

    return NextResponse.json({ task }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Create task error:', error);
    return NextResponse.json(
      { error: 'Failed to create task' },
      { status: 500 }
    );
  }
}

export const POST = withAuth(createTaskHandler);