import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { db } from '@/lib/db';
import { withAuth } from '@/lib/middleware';

const updateTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required').optional(),
  description: z.string().optional(),
  columnId: z.string().min(1, 'Column ID is required').optional(),
  position: z.number().int().min(0, 'Position must be a non-negative integer').optional(),
});

// PUT update task
async function updateTaskHandler(request: NextRequest, user: any, { params }: { params: { id: string } }) {
  try {
    const body = await request.json();
    const { title, description, columnId, position } = updateTaskSchema.parse(body);

    // Check if task exists and belongs to user's board
    const task = await db.task.findFirst({
      where: {
        id: params.id,
        column: {
          board: {
            userId: user.id
          }
        }
      }
    });

    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    // If changing column, verify new column belongs to user's board
    if (columnId && columnId !== task.columnId) {
      const newColumn = await db.column.findFirst({
        where: {
          id: columnId,
          board: {
            userId: user.id
          }
        }
      });

      if (!newColumn) {
        return NextResponse.json(
          { error: 'Column not found' },
          { status: 404 }
        );
      }
    }

    const updatedTask = await db.task.update({
      where: { id: params.id },
      data: {
        ...(title !== undefined && { title }),
        ...(description !== undefined && { description }),
        ...(columnId !== undefined && { columnId }),
        ...(position !== undefined && { position })
      }
    });

    return NextResponse.json({ task: updatedTask });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Update task error:', error);
    return NextResponse.json(
      { error: 'Failed to update task' },
      { status: 500 }
    );
  }
}

// DELETE task
async function deleteTaskHandler(request: NextRequest, user: any, { params }: { params: { id: string } }) {
  try {
    // Check if task exists and belongs to user's board
    const task = await db.task.findFirst({
      where: {
        id: params.id,
        column: {
          board: {
            userId: user.id
          }
        }
      }
    });

    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    await db.task.delete({
      where: { id: params.id }
    });

    return NextResponse.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Delete task error:', error);
    return NextResponse.json(
      { error: 'Failed to delete task' },
      { status: 500 }
    );
  }
}

export const PUT = (request: NextRequest, context: any) => 
  withAuth((req: NextRequest, user: any) => updateTaskHandler(req, user, context))(request);

export const DELETE = (request: NextRequest, context: any) => 
  withAuth((req: NextRequest, user: any) => deleteTaskHandler(req, user, context))(request);