import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from "@/hooks/use-auth";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Kanban Board - Task Management",
  description: "A modern Kanban board application for task management",
  keywords: ["Kanban", "Task Management", "Productivity", "Next.js", "TypeScript"],
  authors: [{ name: "Kanban Board Team" }],
  openGraph: {
    title: "Kanban Board",
    description: "Modern task management with Kanban boards",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Kanban Board",
    description: "Modern task management with Kanban boards",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground`}
      >
        <AuthProvider>
          {children}
          <Toaster />
        </AuthProvider>
      </body>
    </html>
  );
}
