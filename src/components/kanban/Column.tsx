'use client';

import { useState } from 'react';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Plus, MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import { SortableTask } from './SortableTask';

interface ColumnProps {
  column: {
    id: string;
    title: string;
    position: number;
    tasks: Array<{
      id: string;
      title: string;
      description?: string;
      position: number;
      columnId: string;
    }>;
  };
  onTaskUpdate: () => void;
}

export function Column({ column, onTaskUpdate }: ColumnProps) {
  const [isCreateTaskOpen, setIsCreateTaskOpen] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskDescription, setNewTaskDescription] = useState('');
  const [isEditColumnOpen, setIsEditColumnOpen] = useState(false);
  const [editColumnTitle, setEditColumnTitle] = useState('');

  const { setNodeRef } = useDroppable({
    id: column.id,
    data: {
      type: 'column',
      column,
    },
  });

  const createTask = async () => {
    if (!newTaskTitle.trim()) return;

    try {
      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: newTaskTitle,
          description: newTaskDescription,
          columnId: column.id,
          position: column.tasks.length,
        }),
      });

      if (response.ok) {
        setNewTaskTitle('');
        setNewTaskDescription('');
        setIsCreateTaskOpen(false);
        onTaskUpdate();
      }
    } catch (error) {
      console.error('Failed to create task:', error);
    }
  };

  const updateColumn = async () => {
    if (!editColumnTitle.trim()) return;

    try {
      const response = await fetch(`/api/columns/${column.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title: editColumnTitle }),
      });

      if (response.ok) {
        setIsEditColumnOpen(false);
        onTaskUpdate();
      }
    } catch (error) {
      console.error('Failed to update column:', error);
    }
  };

  const deleteColumn = async () => {
    try {
      const response = await fetch(`/api/columns/${column.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        onTaskUpdate();
      }
    } catch (error) {
      console.error('Failed to delete column:', error);
    }
  };

  return (
    <Card className="w-80 flex-shrink-0 bg-muted/50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-lg">{column.title}</h3>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  setEditColumnTitle(column.title);
                  setIsEditColumnOpen(true);
                }}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Column
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={deleteColumn}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Column
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div ref={setNodeRef} className="min-h-[200px] space-y-3">
          <SortableContext items={column.tasks.map(task => task.id)} strategy={verticalListSortingStrategy}>
            {column.tasks.map((task) => (
              <SortableTask key={task.id} task={task} onTaskUpdate={onTaskUpdate} />
            ))}
          </SortableContext>
        </div>

        <Dialog open={isCreateTaskOpen} onOpenChange={setIsCreateTaskOpen}>
          <DialogTrigger asChild>
            <Button variant="ghost" className="w-full justify-start h-8 px-2 text-muted-foreground hover:text-foreground">
              <Plus className="h-4 w-4 mr-2" />
              Add task
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Task</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Input
                placeholder="Task title"
                value={newTaskTitle}
                onChange={(e) => setNewTaskTitle(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && createTask()}
              />
              <Input
                placeholder="Description (optional)"
                value={newTaskDescription}
                onChange={(e) => setNewTaskDescription(e.target.value)}
              />
              <Button onClick={createTask} className="w-full">
                Create Task
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit Column Dialog */}
        <Dialog open={isEditColumnOpen} onOpenChange={setIsEditColumnOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Column</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Input
                placeholder="Column title"
                value={editColumnTitle}
                onChange={(e) => setEditColumnTitle(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && updateColumn()}
              />
              <div className="flex gap-2">
                <Button onClick={updateColumn} className="flex-1">
                  Save Changes
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsEditColumnOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}