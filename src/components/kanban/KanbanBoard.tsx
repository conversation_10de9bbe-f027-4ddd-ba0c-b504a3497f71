'use client';

import { useState, useEffect } from 'react';
import { DndContext, DragEndEvent, DragOverEvent, DragStartEvent, closestCenter, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, arrayMove, horizontalListSortingStrategy } from '@dnd-kit/sortable';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, MoreHorizontal } from 'lucide-react';
import { Column } from './Column';
import { Task } from './Task';

interface BoardData {
  id: string;
  name: string;
  columns: ColumnData[];
}

interface ColumnData {
  id: string;
  title: string;
  position: number;
  tasks: TaskData[];
}

interface TaskData {
  id: string;
  title: string;
  description?: string;
  position: number;
  columnId: string;
}

export function KanbanBoard() {
  const [boards, setBoards] = useState<BoardData[]>([]);
  const [currentBoard, setCurrentBoard] = useState<BoardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateBoardOpen, setIsCreateBoardOpen] = useState(false);
  const [newBoardName, setNewBoardName] = useState('');

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  useEffect(() => {
    fetchBoards();
  }, []);

  const fetchBoards = async () => {
    try {
      const response = await fetch('/api/boards');
      if (response.ok) {
        const data = await response.json();
        setBoards(data.boards);
        if (data.boards.length > 0) {
          setCurrentBoard(data.boards[0]);
        }
      }
    } catch (error) {
      console.error('Failed to fetch boards:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createBoard = async () => {
    if (!newBoardName.trim()) return;

    try {
      const response = await fetch('/api/boards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newBoardName }),
      });

      if (response.ok) {
        const data = await response.json();
        setBoards(prev => [data.board, ...prev]);
        setCurrentBoard(data.board);
        setNewBoardName('');
        setIsCreateBoardOpen(false);
      }
    } catch (error) {
      console.error('Failed to create board:', error);
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    // Handle drag start if needed
  };

  const handleDragOver = (event: DragOverEvent) => {
    // Handle drag over if needed
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    const activeId = active.id.toString();
    const overId = over.id.toString();

    // Handle task movement between columns
    if (active.data.current?.type === 'task' && over.data.current?.type === 'column') {
      const taskId = activeId;
      const newColumnId = overId;

      try {
        const response = await fetch(`/api/tasks/${taskId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ columnId: newColumnId }),
        });

        if (response.ok) {
          // Refresh board data
          fetchBoards();
        }
      } catch (error) {
        console.error('Failed to move task:', error);
      }
    }

    // Handle column reordering
    if (active.data.current?.type === 'column' && over.data.current?.type === 'column') {
      if (activeId !== overId) {
        const oldIndex = currentBoard!.columns.findIndex(col => col.id === activeId);
        const newIndex = currentBoard!.columns.findIndex(col => col.id === overId);

        const newColumns = arrayMove(currentBoard!.columns, oldIndex, newIndex);
        const updatedColumns = newColumns.map((col, index) => ({
          ...col,
          position: index
        }));

        // Update column positions in backend
        try {
          await Promise.all(
            updatedColumns.map(col =>
              fetch(`/api/columns/${col.id}`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ position: col.position }),
              })
            )
          );

          setCurrentBoard(prev => ({
            ...prev!,
            columns: updatedColumns
          }));
        } catch (error) {
          console.error('Failed to update column positions:', error);
        }
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!currentBoard) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold mb-4">No boards yet</h2>
        <Dialog open={isCreateBoardOpen} onOpenChange={setIsCreateBoardOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create your first board
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Board</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Input
                placeholder="Board name"
                value={newBoardName}
                onChange={(e) => setNewBoardName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && createBoard()}
              />
              <Button onClick={createBoard} className="w-full">
                Create Board
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Board Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-3xl font-bold">{currentBoard.name}</h2>
          <Dialog open={isCreateBoardOpen} onOpenChange={setIsCreateBoardOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                New Board
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Board</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <Input
                  placeholder="Board name"
                  value={newBoardName}
                  onChange={(e) => setNewBoardName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && createBoard()}
                />
                <Button onClick={createBoard} className="w-full">
                  Create Board
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        <Button variant="outline" size="sm">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </div>

      {/* Board Selector */}
      {boards.length > 1 && (
        <div className="flex gap-2 overflow-x-auto pb-2">
          {boards.map((board) => (
            <Button
              key={board.id}
              variant={currentBoard.id === board.id ? "default" : "outline"}
              size="sm"
              onClick={() => setCurrentBoard(board)}
            >
              {board.name}
            </Button>
          ))}
        </div>
      )}

      {/* Kanban Board */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="flex gap-6 overflow-x-auto pb-6">
          <SortableContext items={currentBoard.columns.map(col => col.id)} strategy={horizontalListSortingStrategy}>
            {currentBoard.columns.map((column) => (
              <Column
                key={column.id}
                column={column}
                onTaskUpdate={() => fetchBoards()}
              />
            ))}
          </SortableContext>
        </div>
      </DndContext>
    </div>
  );
}