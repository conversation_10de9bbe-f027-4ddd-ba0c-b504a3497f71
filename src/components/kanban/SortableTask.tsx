'use client';

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Task } from './Task';

interface SortableTaskProps {
  task: {
    id: string;
    title: string;
    description?: string;
    position: number;
    columnId: string;
  };
  onTaskUpdate: () => void;
}

export function SortableTask({ task, onTaskUpdate }: SortableTaskProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: task.id,
    data: {
      type: 'task',
      task,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    >
      <Task task={task} onTaskUpdate={onTaskUpdate} />
    </div>
  );
}