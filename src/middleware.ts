import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyToken } from './lib/auth';

export function middleware(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value;
  
  // Protected routes that require authentication
  const protectedPaths = ['/'];
  
  // Auth routes that should be accessible only when not authenticated
  const authPaths = ['/auth/login', '/auth/register'];
  
  const { pathname } = request.nextUrl;
  
  // Check if the current path is protected
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));
  
  // Check if the current path is an auth path
  const isAuthPath = authPaths.some(path => pathname.startsWith(path));
  
  // If accessing protected route without token, redirect to home (which will show auth form)
  if (isProtectedPath && !token) {
    // Allow access to home page, it will handle showing auth form
    if (pathname === '/') {
      return NextResponse.next();
    }
    return NextResponse.redirect(new URL('/', request.url));
  }
  
  // If accessing auth route with valid token, redirect to home
  if (isAuthPath && token) {
    const user = verifyToken(token);
    if (user) {
      return NextResponse.redirect(new URL('/', request.url));
    }
  }
  
  // For API routes, let the individual route handlers handle authentication
  if (pathname.startsWith('/api/')) {
    return NextResponse.next();
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};