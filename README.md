# 🎯 Kanban Board Application

A modern, full-stack Kanban board application built with Next.js 15, TypeScript, and Tailwind CSS. Features drag-and-drop task management, user authentication, and a responsive design.

## ✨ Features

### 🔐 Authentication
- User registration and login with secure password hashing
- JWT-based session management
- Protected routes and authentication middleware

### 🎯 Kanban Board Features
- Create and manage multiple boards
- Drag-and-drop task management between columns
- Create, edit, and delete tasks
- Customizable columns (To Do, In Progress, Done by default)
- Real-time updates with smooth animations

### 🎨 User Interface
- Modern, responsive design with Tailwind CSS
- shadcn/ui components for consistent styling
- Drag-and-drop functionality with DND Kit
- Dark/light theme support
- Mobile-friendly interface

### 🗄️ Backend & Database
- RESTful API endpoints with Next.js App Router
- SQLite database with Prisma ORM
- Type-safe database operations
- Efficient data fetching with TanStack Query

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Development Setup

```bash
# Clone the repository
git clone <repository-url>
cd kanban-board

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env

# Generate Prisma client
npm run db:generate

# Push database schema
npm run db:push

# Start development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application running.

### Environment Variables

Create a `.env` file in the root directory:

```env
# Database
DATABASE_URL="file:./dev.db"

# JWT Secret (generate a random secret for production)
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# Node Environment
NODE_ENV="development"
```

## 🐳 Docker Setup

### Using Docker Compose

```bash
# Build and start the application
docker-compose up --build

# Run in background
docker-compose up -d

# Stop the application
docker-compose down
```

The application will be available at [http://localhost:3000](http://localhost:3000).

### Manual Docker Build

```bash
# Build the Docker image
docker build -t kanban-board .

# Run the container
docker run -p 3000:3000 -v $(pwd)/db:/app/db kanban-board
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   │   ├── auth/          # Authentication endpoints
│   │   ├── boards/        # Board CRUD operations
│   │   ├── columns/       # Column CRUD operations
│   │   └── tasks/         # Task CRUD operations
│   ├── layout.tsx         # Root layout with providers
│   ├── page.tsx           # Main application page
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── auth/             # Authentication components
│   ├── kanban/           # Kanban board components
│   └── ui/               # shadcn/ui components
├── hooks/                # Custom React hooks
│   ├── use-auth.ts       # Authentication hook
│   └── use-toast.ts      # Toast notifications
└── lib/                  # Utility functions
    ├── auth.ts           # Authentication utilities
    ├── db.ts             # Database connection
    ├── middleware.ts     # Authentication middleware
    └── utils.ts          # General utilities
```

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Database
npm run db:push      # Push schema to database
npm run db:generate  # Generate Prisma client
npm run db:migrate   # Run database migrations
npm run db:reset     # Reset database
```

## 🎯 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Boards
- `GET /api/boards` - Get user's boards
- `POST /api/boards` - Create new board
- `GET /api/boards/[id]` - Get specific board
- `PUT /api/boards/[id]` - Update board
- `DELETE /api/boards/[id]` - Delete board

### Columns
- `POST /api/columns` - Create new column
- `PUT /api/columns/[id]` - Update column
- `DELETE /api/columns/[id]` - Delete column

### Tasks
- `POST /api/tasks` - Create new task
- `PUT /api/tasks/[id]` - Update task
- `DELETE /api/tasks/[id]` - Delete task

## 🗄️ Database Schema

The application uses the following main entities:

### Users
- `id` - Unique identifier
- `email` - User email (unique)
- `username` - Username (unique)
- `password_hash` - Hashed password
- `created_at` - Account creation timestamp
- `updated_at` - Last update timestamp

### Boards
- `id` - Unique identifier
- `name` - Board name
- `user_id` - Owner user ID
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

### Columns
- `id` - Unique identifier
- `title` - Column title
- `position` - Column order position
- `board_id` - Parent board ID
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

### Tasks
- `id` - Unique identifier
- `title` - Task title
- `description` - Task description (optional)
- `position` - Task order position
- `column_id` - Parent column ID
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

## 🎨 Usage Guide

### Getting Started

1. **Register an Account**: Create a new user account with email, username, and password
2. **Login**: Use your credentials to access the application
3. **Create a Board**: Click "New Board" to create your first Kanban board
4. **Add Tasks**: Click "Add task" in any column to create new tasks
5. **Drag & Drop**: Drag tasks between columns to update their status
6. **Edit Tasks**: Click the edit icon to modify task details
7. **Delete Tasks**: Click the delete icon to remove tasks

### Board Management

- **Switch Boards**: Use the board selector to switch between different boards
- **Create Boards**: Each user can create multiple boards for different projects
- **Default Columns**: New boards come with "To Do", "In Progress", and "Done" columns

### Task Management

- **Create Tasks**: Add tasks with titles and optional descriptions
- **Edit Tasks**: Modify task titles and descriptions
- **Move Tasks**: Drag tasks between columns to change their status
- **Delete Tasks**: Remove tasks that are no longer needed

## 🔒 Security Features

- **Password Hashing**: Uses bcryptjs for secure password storage
- **JWT Authentication**: Stateless authentication with JSON Web Tokens
- **Protected Routes**: Authentication middleware protects sensitive routes
- **Input Validation**: Zod schema validation for all API inputs
- **SQL Injection Prevention**: Prisma ORM provides parameterized queries
- **CORS Protection**: Proper CORS configuration for API routes

## 🚀 Deployment

### Production Build

```bash
# Build the application
npm run build

# Start production server
npm start
```

### Environment Variables for Production

```env
NODE_ENV="production"
DATABASE_URL="file:./prod.db"
JWT_SECRET="your-production-jwt-secret"
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose -f docker-compose.yml up -d

# Or with manual Docker commands
docker build -t kanban-board .
docker run -d -p 3000:3000 --name kanban-app kanban-board
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Components from [shadcn/ui](https://ui.shadcn.com/)
- Drag & Drop by [DND Kit](https://dndkit.com/)
- Database by [Prisma](https://prisma.io/)
- Icons by [Lucide](https://lucide.dev/)

---

Built with ❤️ for the developer community. 🚀
